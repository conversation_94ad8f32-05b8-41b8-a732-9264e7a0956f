
# ???????????
server.port=8081

# ???? Cookie ??????????????????
server.servlet.session.cookie.name=OAUTH2-CLIENT-SESSIONID${server.port}

# ?? OAuth2 ??????????
oauth2-server-url: http://localhost:8080

# ?? OAuth2 ??? ID ???
security.oauth2.client.client-id=client
security.oauth2.client.client-secret=123123

# ?????????????? URI???????????
security.oauth2.client.user-authorization-uri=${oauth2-server-url}/oauth/authorize
security.oauth2.client.access-token-uri=${oauth2-server-url}/oauth/token

# ?? JWT ????????????????
security.oauth2.resource.jwt.key-uri=${oauth2-server-url}/oauth/token_key
