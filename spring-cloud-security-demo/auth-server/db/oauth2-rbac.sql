CREATE TABLE `tb_permission`
(
    `id`          BIGINT(20)   NOT NULL AUTO_INCREMENT,
    `parent_id`   BIGINT(20)   DEFAULT NULL COMMENT '父权限',
    `name`        VARCHAR(64)  NOT NULL COMMENT '权限名称',
    `enname`      VARCHAR(64)  NOT NULL COMMENT '权限英文名称',
    `url`         VARCHAR(255) NOT NULL COMMENT '授权路径',
    `description` VARCHAR(200) DEFAULT NULL COMMENT '备注',
    `created`     DATETIME     NOT NULL,
    `updated`     DATETIME     NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 44
  DEFAULT CHARSET = utf8 COMMENT ='权限表';
INSERT INTO `tb_permission`(`id`, `parent_id`, `name`, `enname`, `url`, `description`, `created`,
                            `updated`)
VALUES (39, 38, '查询订单', 'orderView', '/order/selectOrderInfoByIdAndUsername', NULL,
        '2019-04-04 15:30:30', '2019-04-04 15:30:43'),
       (45, 44, '查询商品', 'productView', '/product/selectProductInfoById', NULL,
        '2019-04-06 23:49:39', '2019-04-06 23:49:41');


CREATE TABLE `tb_role`
(
    `id`          BIGINT(20)  NOT NULL AUTO_INCREMENT,
    `parent_id`   BIGINT(20)   DEFAULT NULL COMMENT '父角色',
    `name`        VARCHAR(64) NOT NULL COMMENT '角色名称',
    `enname`      VARCHAR(64) NOT NULL COMMENT '角色英文名称',
    `description` VARCHAR(200) DEFAULT NULL COMMENT '备注',
    `created`     DATETIME    NOT NULL,
    `updated`     DATETIME    NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 38
  DEFAULT CHARSET = utf8 COMMENT ='角色表';
INSERT INTO `tb_role`(`id`, `parent_id`, `name`, `enname`, `description`, `created`, `updated`)
VALUES (37, 0, '超级管理员', 'admin', NULL, '2019-04-04 23:22:03', '2019-04-04 23:22:05');

CREATE TABLE `tb_role_permission`
(
    `id`            BIGINT(20) NOT NULL AUTO_INCREMENT,
    `role_id`       BIGINT(20) NOT NULL COMMENT '角色 ID',
    `permission_id` BIGINT(20) NOT NULL COMMENT '权限 ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 43
  DEFAULT CHARSET = utf8 COMMENT ='角色权限表';
INSERT INTO `tb_role_permission`(`id`, `role_id`, `permission_id`)
VALUES (37, 37, 37),
       (38, 37, 38),
       (39, 37, 39),
       (40, 37, 40),
       (41, 37, 41),
       (42, 37, 42),
       (43, 37, 44),
       (44, 37, 45),
       (45, 37, 46),
       (46, 37, 47),
       (47, 37, 48);

CREATE TABLE `tb_user`
(
    `id`       BIGINT(20)  NOT NULL AUTO_INCREMENT,
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password` VARCHAR(64) NOT NULL COMMENT '密码，加密存储',
    `phone`    VARCHAR(20) DEFAULT NULL COMMENT '注册手机号',
    `email`    VARCHAR(50) DEFAULT NULL COMMENT '注册邮箱',
    `created`  DATETIME    NOT NULL,
    `updated`  DATETIME    NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `username` (`username`) USING BTREE,
    UNIQUE KEY `phone` (`phone`) USING BTREE,
    UNIQUE KEY `email` (`email`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 38
  DEFAULT CHARSET = utf8 COMMENT ='用户表';
INSERT INTO `tb_user`(`id`, `username`, `password`, `phone`, `email`, `created`, `updated`)
VALUES (37, 'fox', '$2a$10$9ZhDOBp.sRKat4l14ygu/.LscxrMUcDAfeVOEPiYwbcRkoB09gCmi', '158xxxxxxx',
        '<EMAIL>', '2019-04-04 23:21:27', '2019-04-04 23:21:29');

CREATE TABLE `tb_user_role`
(
    `id`      BIGINT(20) NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT(20) NOT NULL COMMENT '用户 ID',
    `role_id` BIGINT(20) NOT NULL COMMENT '角色 ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 38
  DEFAULT CHARSET = utf8 COMMENT ='用户角色表';
INSERT INTO `tb_user_role`(`id`, `user_id`, `role_id`)
VALUES (37, 37, 37);
