# https://github.com/spring-projects/spring-security-oauth/blob/master/spring-security-oauth2/src/test/resources/schema.sql

CREATE TABLE `clientdetails`
(
    `appId`                  varchar(128) NOT NULL,
    `resourceIds`            varchar(255)  DEFAULT NULL,
    `appSecret`              varchar(255)  DEFAULT NULL,
    `scope`                  varchar(255)  DEFAULT NULL,
    `grantTypes`             varchar(255)  DEFAULT NULL,
    `redirectUrl`            varchar(255)  DEFAULT NULL,
    `authorities`            varchar(255)  DEFAULT NULL,
    `access_token_validity`  int(11)       DEFAULT NULL,
    `refresh_token_validity` int(11)       DEFAULT NULL,
    `additionalInformation`  varchar(4096) DEFAULT NULL,
    `autoApproveScopes`      varchar(255)  DEFAULT NULL,
    PRIMARY KEY (`appId`)
) ENGINE = InnoDB
  DEFAULT CHARSET = UTF8;

CREATE TABLE `oauth_access_token`
(
    `token_id`          varchar(255) DEFAULT NULL,
    `token`             blob,
    `authentication_id` varchar(128) NOT NULL,
    `user_name`         varchar(255) DEFAULT NULL,
    `client_id`         varchar(255) DEFAULT NULL,
    `authentication`    blob,
    `refresh_token`     varchar(255) DEFAULT NULL,
    PRIMARY KEY (`authentication_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = UTF8;

CREATE TABLE `oauth_approvals`
(
    `userId`         varchar(255)   DEFAULT NULL,
    `clientId`       varchar(255)   DEFAULT NULL,
    `scope`          varchar(255)   DEFAULT NULL,
    `status`         varchar(10)    DEFAULT NULL,
    `expiresAt`      timestamp NULL DEFAULT NULL,
    `lastModifiedAt` timestamp NULL DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = UTF8;

CREATE TABLE `oauth_client_details`
(
    `client_id`               varchar(128) NOT NULL,
    `resource_ids`            varchar(255)  DEFAULT NULL,
    `client_secret`           varchar(255)  DEFAULT NULL,
    `scope`                   varchar(255)  DEFAULT NULL,
    `authorized_grant_types`  varchar(255)  DEFAULT NULL,
    `web_server_redirect_uri` varchar(255)  DEFAULT NULL,
    `authorities`             varchar(255)  DEFAULT NULL,
    `access_token_validity`   int(11)       DEFAULT NULL,
    `refresh_token_validity`  int(11)       DEFAULT NULL,
    `additional_information`  varchar(4096) DEFAULT NULL,
    `autoapprove`             varchar(255)  DEFAULT NULL,
    PRIMARY KEY (`client_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = UTF8;

CREATE TABLE `oauth_client_token`
(
    `token_id`          varchar(255) DEFAULT NULL,
    `token`             blob,
    `authentication_id` varchar(128) NOT NULL,
    `user_name`         varchar(255) DEFAULT NULL,
    `client_id`         varchar(255) DEFAULT NULL,
    PRIMARY KEY (`authentication_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = UTF8;

CREATE TABLE `oauth_code`
(
    `code`           varchar(255) DEFAULT NULL,
    `authentication` blob
) ENGINE = InnoDB
  DEFAULT CHARSET = UTF8;

CREATE TABLE `oauth_refresh_token`
(
    `token_id`       varchar(255) DEFAULT NULL,
    `token`          blob,
    `authentication` blob
) ENGINE = InnoDB
  DEFAULT CHARSET = UTF8;
