-- OAuth2客户端配置数据
-- 注意：client_secret使用BCrypt加密，这里的密码是123123

INSERT INTO `oauth_client_details` (
    `client_id`,
    `resource_ids`,
    `client_secret`,
    `scope`,
    `authorized_grant_types`,
    `web_server_redirect_uri`,
    `authorities`,
    `access_token_validity`,
    `refresh_token_validity`,
    `additional_information`,
    `autoapprove`
) VALUES (
    'gateway-server',
    NULL,
    '$2a$10$7MXOimmqhbi3/w8jb/ZMLuxyIdBadC67GNspfVfC/.vxCb9ze9ZQ6', -- 123123经过BCrypt加密
    'read,write',
    'password,refresh_token,authorization_code,client_credentials',
    'http://localhost:8080/login',
    NULL,
    3600,
    86400,
    NULL,
    'false'
);

-- 添加一个通用的客户端配置用于测试
INSERT INTO `oauth_client_details` (
    `client_id`,
    `resource_ids`,
    `client_secret`,
    `scope`,
    `authorized_grant_types`,
    `web_server_redirect_uri`,
    `authorities`,
    `access_token_validity`,
    `refresh_token_validity`,
    `additional_information`,
    `autoapprove`
) VALUES (
    'client',
    NULL,
    '$2a$10$7MXOimmqhbi3/w8jb/ZMLuxyIdBadC67GNspfVfC/.vxCb9ze9ZQ6', -- 123123经过BCrypt加密
    'all',
    'password,refresh_token,authorization_code,client_credentials',
    'http://www.baidu.com',
    NULL,
    3600,
    86400,
    NULL,
    'false'
);

-- 注意：实际使用时，请使用正确的BCrypt加密密码
-- 可以使用以下Java代码生成：
-- BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
-- String encodedPassword = encoder.encode("123123");
-- System.out.println(encodedPassword);
