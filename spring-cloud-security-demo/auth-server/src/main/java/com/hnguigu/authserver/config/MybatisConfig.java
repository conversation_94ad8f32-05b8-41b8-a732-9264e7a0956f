package com.hnguigu.authserver.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@MapperScan("com.hnguigu.authserver.mapper")
public class MybatisConfig {

//    @ConfigurationProperties("spring.datasource")
//    @Bean
//    public DataSource dataSource() {
//        return new HikariDataSource();
//    }
}
