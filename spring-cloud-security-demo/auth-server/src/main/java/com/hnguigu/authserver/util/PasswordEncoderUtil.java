package com.hnguigu.authserver.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 密码编码工具类
 * 用于生成BCrypt加密的密码，主要用于客户端密钥和用户密码的加密
 * 
 * <AUTHOR>
 */
public class PasswordEncoderUtil {
    
    private static final BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
    
    /**
     * 对密码进行BCrypt加密
     * @param rawPassword 原始密码
     * @return 加密后的密码
     */
    public static String encode(String rawPassword) {
        return encoder.encode(rawPassword);
    }
    
    /**
     * 验证密码是否匹配
     * @param rawPassword 原始密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    public static boolean matches(String rawPassword, String encodedPassword) {
        return encoder.matches(rawPassword, encodedPassword);
    }
    
    /**
     * 主方法，用于生成加密密码
     * 运行此方法可以生成用于数据库的加密密码
     */
    public static void main(String[] args) {
        // 生成客户端密钥123123的加密版本
        String clientSecret = "123123";
        String encodedClientSecret = encode(clientSecret);
        System.out.println("客户端密钥 '" + clientSecret + "' 的BCrypt加密结果:");
        System.out.println(encodedClientSecret);
        
        // 生成用户密码123456的加密版本
        String userPassword = "123456";
        String encodedUserPassword = encode(userPassword);
        System.out.println("\n用户密码 '" + userPassword + "' 的BCrypt加密结果:");
        System.out.println(encodedUserPassword);
        
        // 验证加密结果
        System.out.println("\n验证结果:");
        System.out.println("客户端密钥验证: " + matches(clientSecret, encodedClientSecret));
        System.out.println("用户密码验证: " + matches(userPassword, encodedUserPassword));
    }
}
