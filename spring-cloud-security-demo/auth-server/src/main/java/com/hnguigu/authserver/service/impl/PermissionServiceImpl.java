package com.hnguigu.authserver.service.impl;

import com.hnguigu.authserver.bean.SysPermission;
import com.hnguigu.authserver.mapper.PermissionMapper;
import com.hnguigu.authserver.service.PermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PermissionServiceImpl implements PermissionService {

    @Autowired
    private PermissionMapper permissionMapper;
    @Override
    public List<SysPermission> selectByUserId(Long userId) {

        return permissionMapper.selectByUserId(userId);
    }
}
