server:
  port: 8888
spring:
  application:
    name: auth-server
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
  redis:
    host: 127.0.0.1
    database: 0

  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************
    username: root
    password: root
    hikari:
      minimum-idle: 5
      idle-timeout: 600000
      maximum-pool-size: 10
      auto-commit: true
      pool-name: MyHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

