# Spring Cloud Security Demo 学习笔记

## 📋 项目架构概览

### 服务端口分配
- **auth-server**: 8888 (认证授权服务器)
- **gateway-server**: 8880 (API网关)
- **order-server**: 8083 (订单服务)
- **product-server**: 8084 (产品服务)
- **nacos**: 8848 (服务注册中心)
- **redis**: 6379 (Token存储)
- **mysql**: 3306 (用户权限数据)

## 🔑 核心组件分析

### 1. AuthenticationFilter (认证过滤器)
- **执行顺序**: @Order(0) - 第一个执行
- **主要功能**: 
  - 提取Authorization头中的Bearer Token
  - 调用auth-server的/oauth/check_token验证Token
  - 将TokenInfo存储到exchange.attributes中
- **白名单URL**: /oauth/token, /oauth/check_token, /user/getCurrentUser

### 2. AuthorizationFilter (授权过滤器)
- **执行顺序**: @Order(1) - 在认证过滤器之后执行
- **主要功能**:
  - 检查Token是否过期 (tokenInfo.isActive())
  - 验证用户权限是否匹配请求路径
  - 权限检查逻辑: currentUrl.contains(userPermissionUrl)

### 3. TokenInfo 数据结构
```java
public class TokenInfo {
    private boolean active;        // Token是否有效
    private String client_id;      // 客户端ID
    private String[] scope;        // 授权范围
    private String username;       // 用户名
    private String[] aud;          // 受众
    private Date exp;             // 过期时间
    private String[] authorities;  // 用户权限列表
}
```

## 🗄️ 数据库设计

### OAuth2标准表 (oauth2.sql)
- `oauth_client_details`: 客户端配置
- `oauth_access_token`: 访问令牌
- `oauth_refresh_token`: 刷新令牌

### RBAC权限表 (oauth2-rbac.sql)
- `tb_user`: 用户表
- `tb_role`: 角色表
- `tb_permission`: 权限表
- `tb_user_role`: 用户角色关联表
- `tb_role_permission`: 角色权限关联表

### 测试数据
- **用户**: fox / 123456
- **客户端**: gateway-server / 123123
- **权限**: /order/selectOrderInfoByIdAndUsername, /product/selectProductInfoById

## 🚀 启动顺序

1. **启动基础服务**
   ```bash
   # 启动Nacos
   sh startup.sh -m standalone
   
   # 启动Redis
   redis-server
   
   # 启动MySQL并导入SQL脚本
   ```

2. **启动微服务**
   ```bash
   # 1. 启动认证服务器
   cd auth-server && mvn spring-boot:run
   
   # 2. 启动网关服务器
   cd gateway-server && mvn spring-boot:run
   
   # 3. 启动业务服务
   cd order-server && mvn spring-boot:run
   cd product-server && mvn spring-boot:run
   ```

## 🧪 测试用例

### 1. 获取Access Token
```bash
curl -X POST 'http://localhost:8888/oauth/token' \
  -H 'Authorization: Basic Z2F0ZXdheS1zZXJ2ZXI6MTIzMTIz' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'username=fox&password=123456&grant_type=password&scope=read'
```

**响应示例**:
```json
{
  "access_token": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
  "token_type": "bearer",
  "refresh_token": "refresh_token_value",
  "expires_in": 3599,
  "scope": "read"
}
```

### 2. 通过网关访问订单服务
```bash
curl -X GET 'http://localhost:8880/order/selectOrderInfoByIdAndUsername?id=1&username=fox' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN'
```

### 3. 通过网关访问产品服务
```bash
curl -X GET 'http://localhost:8880/product/selectProductInfoById?id=1' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN'
```

## 🔍 调试技巧

### 1. 查看Token信息
```bash
curl -X POST 'http://localhost:8888/oauth/check_token' \
  -H 'Authorization: Basic Z2F0ZXdheS1zZXJ2ZXI6MTIzMTIz' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'token=YOUR_ACCESS_TOKEN'
```

### 2. 直接访问业务服务(绕过网关)
```bash
# 直接访问订单服务
curl -X GET 'http://localhost:8083/order/selectOrderInfoByIdAndUsername?id=1&username=fox'

# 直接访问产品服务  
curl -X GET 'http://localhost:8084/product/selectProductInfoById?id=1'
```

## 📝 学习任务

### 基础任务
- [ ] 成功启动所有服务
- [ ] 完成Token获取和验证流程
- [ ] 理解过滤器链的执行顺序
- [ ] 分析权限检查逻辑

### 进阶任务
- [ ] 修改用户权限，测试访问控制
- [ ] 添加新的业务接口并配置权限
- [ ] 自定义异常处理
- [ ] 实现Token刷新机制

### 高级任务
- [ ] 集成JWT替代Redis存储
- [ ] 实现细粒度权限控制
- [ ] 添加API限流功能
- [ ] 集成链路追踪

## 🐛 常见问题

### 1. Token验证失败
- 检查Authorization头格式: `Bearer token_value`
- 确认Token未过期
- 验证客户端认证信息

### 2. 权限不足
- 检查用户权限配置
- 确认URL路径匹配逻辑
- 查看数据库权限数据

### 3. 服务无法注册到Nacos
- 检查Nacos服务状态
- 确认网络连接
- 查看application.yaml配置

## 📚 扩展学习

1. **OAuth2深入理解**
   - 四种授权模式详解
   - Token生命周期管理
   - 安全最佳实践

2. **Spring Security原理**
   - 过滤器链机制
   - 认证和授权流程
   - 自定义安全配置

3. **微服务安全架构**
   - 服务间认证
   - API网关安全
   - 分布式会话管理
