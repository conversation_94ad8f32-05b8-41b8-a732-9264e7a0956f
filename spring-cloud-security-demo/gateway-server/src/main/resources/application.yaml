server:
  port: 8880
spring:
  application:
    name: gateway-server

  cloud:
    gateway:
      discovery:
        locator:
          lower-case-service-id: true
          enabled: true
      routes:
      - id: product-server
        uri: lb://product-server
        predicates:
        - Path=/product/**
      - id: order_server
        uri: lb://order-server
        predicates:
        - Path=/order/**
      - id: auth_server
        uri: lb://auth-server
        predicates:
        - Path=/oauth/**,/user/**
    nacos:
      discovery:
        server-addr: localhost:8848
  main:
    allow-bean-definition-overriding: true

