package com.hnguigu.gatewayserver.filter;

import com.hnguigu.gatewayserver.common.MDA;
import com.hnguigu.gatewayserver.common.TokenInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.annotation.Order;
import org.springframework.http.*;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.LinkedHashSet;
import java.util.Set;

/**
 * 认证过滤器
 *
 * <AUTHOR>
 */
@Component
@Order(0)
//自定义局部过滤器
public class AuthenticationFilter implements GlobalFilter, InitializingBean {

    @Autowired
    private RestTemplate restTemplate;

    private static Set<String> shouldSkipUrl = new LinkedHashSet<>();

//    InitializingBean通过 afterPropertiesSet() 实现初始化逻辑 可用 @PostConstruct  代替
    @Override
    public void afterPropertiesSet() throws Exception {
        // 不拦截认证的请求
        shouldSkipUrl.add("/oauth/token");
        shouldSkipUrl.add("/oauth/check_token");
        shouldSkipUrl.add("/user/getCurrentUser");
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {

        /*exchange
        这是 Spring WebFlux 提供的一个接口，表示当前 HTTP 请求和响应的上下文。
        它包含了请求信息（如请求头、路径、参数等）和响应信息（如响应头、状态码等）。
        通过 exchange，可以访问请求的详细信息并对响应进行修改*/

        /*chain
        这是 Spring Cloud Gateway 提供的过滤器链对象。
        它用于将当前请求传递给下一个过滤器。
        调用 chain.filter(exchange) 会将请求交给下一个过滤器进行处理，最终到达目标服务。
        这两个参数是实现全局过滤器的核心，用于拦截和处理请求。*/

        ServerHttpResponse response = exchange.getResponse();
        String requestPath = exchange.getRequest().getURI().getPath();

        //不需要认证的url
        if (shouldSkip(requestPath)) {
            return chain.filter(exchange);
        }

        //获取请求头
        String authHeader = exchange.getRequest().getHeaders().getFirst("Authorization");

        //请求头为空
        if (StringUtils.isEmpty(authHeader)) {
           /* response.setStatusCode(HttpStatus.UNAUTHORIZED);
            response.setComplete();*/
            throw new RuntimeException("请求头为空");
        }

        TokenInfo tokenInfo = null;
        try {
            //获取token信息
            tokenInfo = getTokenInfo(authHeader);
        } catch (Exception e) {
            throw new RuntimeException("校验令牌异常");
        }
        // tokenInfo
        exchange.getAttributes().put("tokenInfo", tokenInfo);
        return chain.filter(exchange);
//        用 chain.filter(exchange) 会将请求交给下一个过滤器进行处理
    }

    private boolean shouldSkip(String reqPath) {
        for (String skipPath : shouldSkipUrl) {
            if (reqPath.contains(skipPath)) {
                return true;
            }
        }
        return false;
    }

    private TokenInfo getTokenInfo(String authHeader) {
        // 往授权服务发请求 /oauth/check_token
        // 获取token的值
        String token = StringUtils.substringAfter(authHeader, "bearer ");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        //必须 basicAuth clienId clientSecret
        headers.setBasicAuth(MDA.clientId, MDA.clientSecret);

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("token", token);

        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(params, headers);

        // 远程调用授权服务器去进行token验证，验证成功则返回数据（用户数据）
        ResponseEntity<TokenInfo> response = restTemplate.exchange(MDA.checkTokenUrl, HttpMethod.POST, entity,
                TokenInfo.class);

        return response.getBody();
    }
}
