#!/bin/bash

# Spring Cloud Security Demo 测试脚本
# 使用方法: ./test-scripts.sh [command]

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务地址配置
AUTH_SERVER="http://localhost:8888"
GATEWAY_SERVER="http://localhost:8880"
ORDER_SERVER="http://localhost:8083"
PRODUCT_SERVER="http://localhost:8084"

# 客户端认证信息 (gateway-server:123123 的Base64编码)
CLIENT_AUTH="Z2F0ZXdheS1zZXJ2ZXI6MTIzMTIz"

# 用户认证信息
USERNAME="fox"
PASSWORD="123456"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查服务状态
check_service() {
    local service_name=$1
    local service_url=$2
    
    print_message $BLUE "检查 $service_name 服务状态..."
    
    if curl -s --connect-timeout 5 "$service_url" > /dev/null 2>&1; then
        print_message $GREEN "✓ $service_name 服务正常运行"
        return 0
    else
        print_message $RED "✗ $service_name 服务无法访问"
        return 1
    fi
}

# 检查所有服务状态
check_all_services() {
    print_message $YELLOW "=== 检查所有服务状态 ==="
    
    check_service "认证服务器" "$AUTH_SERVER/actuator/health"
    check_service "网关服务器" "$GATEWAY_SERVER/actuator/health"
    check_service "订单服务" "$ORDER_SERVER/actuator/health"
    check_service "产品服务" "$PRODUCT_SERVER/actuator/health"
    
    echo ""
}

# 获取访问令牌
get_access_token() {
    print_message $YELLOW "=== 获取访问令牌 ==="
    
    local response=$(curl -s -X POST "$AUTH_SERVER/oauth/token" \
        -H "Authorization: Basic $CLIENT_AUTH" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "username=$USERNAME&password=$PASSWORD&grant_type=password&scope=read")
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ 令牌获取成功"
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
        
        # 提取access_token
        ACCESS_TOKEN=$(echo "$response" | jq -r '.access_token' 2>/dev/null)
        if [ "$ACCESS_TOKEN" != "null" ] && [ -n "$ACCESS_TOKEN" ]; then
            print_message $GREEN "Access Token: $ACCESS_TOKEN"
            echo "$ACCESS_TOKEN" > .access_token
        else
            print_message $RED "✗ 无法提取访问令牌"
        fi
    else
        print_message $RED "✗ 令牌获取失败"
    fi
    
    echo ""
}

# 验证令牌
check_token() {
    print_message $YELLOW "=== 验证访问令牌 ==="
    
    if [ -f .access_token ]; then
        ACCESS_TOKEN=$(cat .access_token)
    else
        print_message $RED "✗ 未找到访问令牌，请先获取令牌"
        return 1
    fi
    
    local response=$(curl -s -X POST "$AUTH_SERVER/oauth/check_token" \
        -H "Authorization: Basic $CLIENT_AUTH" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "token=$ACCESS_TOKEN")
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ 令牌验证成功"
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
    else
        print_message $RED "✗ 令牌验证失败"
    fi
    
    echo ""
}

# 通过网关访问订单服务
test_order_service_via_gateway() {
    print_message $YELLOW "=== 通过网关访问订单服务 ==="
    
    if [ -f .access_token ]; then
        ACCESS_TOKEN=$(cat .access_token)
    else
        print_message $RED "✗ 未找到访问令牌，请先获取令牌"
        return 1
    fi
    
    local response=$(curl -s -X GET "$GATEWAY_SERVER/order/selectOrderInfoByIdAndUsername?id=1&username=fox" \
        -H "Authorization: Bearer $ACCESS_TOKEN")
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ 订单服务访问成功"
        echo "响应: $response"
    else
        print_message $RED "✗ 订单服务访问失败"
    fi
    
    echo ""
}

# 通过网关访问产品服务
test_product_service_via_gateway() {
    print_message $YELLOW "=== 通过网关访问产品服务 ==="
    
    if [ -f .access_token ]; then
        ACCESS_TOKEN=$(cat .access_token)
    else
        print_message $RED "✗ 未找到访问令牌，请先获取令牌"
        return 1
    fi
    
    local response=$(curl -s -X GET "$GATEWAY_SERVER/product/selectProductInfoById?id=1" \
        -H "Authorization: Bearer $ACCESS_TOKEN")
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ 产品服务访问成功"
        echo "响应: $response"
    else
        print_message $RED "✗ 产品服务访问失败"
    fi
    
    echo ""
}

# 直接访问业务服务(绕过网关)
test_direct_access() {
    print_message $YELLOW "=== 直接访问业务服务(绕过网关) ==="
    
    print_message $BLUE "访问订单服务..."
    local order_response=$(curl -s -X GET "$ORDER_SERVER/order/selectOrderInfoByIdAndUsername?id=1&username=fox")
    print_message $GREEN "订单服务响应: $order_response"
    
    print_message $BLUE "访问产品服务..."
    local product_response=$(curl -s -X GET "$PRODUCT_SERVER/product/selectProductInfoById?id=1")
    print_message $GREEN "产品服务响应: $product_response"
    
    echo ""
}

# 测试无权限访问
test_unauthorized_access() {
    print_message $YELLOW "=== 测试无权限访问 ==="
    
    print_message $BLUE "使用无效Token访问..."
    local response=$(curl -s -X GET "$GATEWAY_SERVER/order/selectOrderInfoByIdAndUsername?id=1&username=fox" \
        -H "Authorization: Bearer invalid_token")
    
    print_message $YELLOW "响应: $response"
    echo ""
}

# 完整测试流程
run_full_test() {
    print_message $BLUE "=========================================="
    print_message $BLUE "  Spring Cloud Security Demo 完整测试"
    print_message $BLUE "=========================================="
    echo ""
    
    check_all_services
    get_access_token
    check_token
    test_order_service_via_gateway
    test_product_service_via_gateway
    test_direct_access
    test_unauthorized_access
    
    print_message $GREEN "=========================================="
    print_message $GREEN "           测试完成！"
    print_message $GREEN "=========================================="
}

# 显示帮助信息
show_help() {
    echo "Spring Cloud Security Demo 测试脚本"
    echo ""
    echo "使用方法: $0 [command]"
    echo ""
    echo "可用命令:"
    echo "  check-services    检查所有服务状态"
    echo "  get-token        获取访问令牌"
    echo "  check-token      验证访问令牌"
    echo "  test-order       通过网关测试订单服务"
    echo "  test-product     通过网关测试产品服务"
    echo "  test-direct      直接访问业务服务"
    echo "  test-unauth      测试无权限访问"
    echo "  full-test        运行完整测试流程"
    echo "  help             显示此帮助信息"
    echo ""
}

# 主函数
main() {
    case "$1" in
        "check-services")
            check_all_services
            ;;
        "get-token")
            get_access_token
            ;;
        "check-token")
            check_token
            ;;
        "test-order")
            test_order_service_via_gateway
            ;;
        "test-product")
            test_product_service_via_gateway
            ;;
        "test-direct")
            test_direct_access
            ;;
        "test-unauth")
            test_unauthorized_access
            ;;
        "full-test")
            run_full_test
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        "")
            run_full_test
            ;;
        *)
            print_message $RED "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
